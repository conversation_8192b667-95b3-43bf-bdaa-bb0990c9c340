<template>
  <!-- Button -->
  <div>
    <MyButton ref="buttonRef">Test Button</MyButton>
    <MyButton plain>Plain Button</MyButton>
    <MyButton round>Round Button</MyButton>
    <MyButton circle>Circle Button</MyButton>
    <MyButton disabled>Disabled <PERSON><PERSON></MyButton>
    <MyButton type="primary">Primary Button</MyButton>
    <MyButton type="success">Success Button</MyButton>
    <MyButton type="warning">Warning Button</MyButton>
    <MyButton type="danger">Danger Button</MyButton>
    <MyButton type="info">Info Button</MyButton>
    <MyButton type="primary" size="large">Large Button</MyButton>
    <MyButton type="primary" size="small">Small Button</MyButton>
    <br />
    <MyButton size="large" loading>loading</MyButton>
    <MyButton size="large" icon="arrow-up">icon</MyButton>
  </div>

  <!-- Collapse -->
  <div>
    <MyCollapse v-model="openedValue" accordion>
      <MyCollapseItem name="a">
        <template #title>
          <h1>title a</h1>
        </template>
        <div>Content a</div>
      </MyCollapseItem>
      <MyCollapseItem name="b" title="Collapse b" disabled>
        <div>Content 2</div>
      </MyCollapseItem>
    </MyCollapse>
  </div>

  <!-- Icon -->
  <div>
    <MyIcon icon="arrow-up" size="10x" type="warning" color="blue" />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import MyButton from './components/Button/Button.vue'
import type { ButtonInstance } from './components/Button/types'
import MyCollapse from './components/Collapse/Collapse.vue'
import MyCollapseItem from './components/Collapse/CollapseItem.vue'
import MyIcon from './components/Icon/Icon.vue'

const buttonRef = ref<ButtonInstance | null>(null)

const openedValue = ref(['a'])

onMounted(() => {
  if (buttonRef.value) {
    console.log(buttonRef.value.ref)
  }
})
</script>

<style lang="scss" scoped></style>
