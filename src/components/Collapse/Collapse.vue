<template>
  <div class="vk-collapse">
    <slot></slot>
  </div>
</template>

<script lang="ts" setup>
import { provide, ref, watch } from 'vue'
import type { NameType, CollapseEmits, CollapseProps } from './types'
import { collapseContextKey } from './types'

defineOptions({
  name: 'VKCollapse',
})

const props = defineProps<CollapseProps>()
const emits = defineEmits<CollapseEmits>()

const activeNames = ref<NameType[]>(props.modelValue)
watch(
  () => props.modelValue,
  () => {
    activeNames.value = props.modelValue
  },
)

if (props.accordion && activeNames.value.length > 1) {
  console.warn('[Element Warn][Collapse]accordion should only have a single panel opened.')
}
const handleItemClick = (item: NameType) => {
  if (props.accordion) {
    // 手风琴模式：检查当前项是否已激活
    activeNames.value = activeNames.value.includes(item) ? [] : [item]
  } else {
    // 普通模式：切换当前项的激活状态
    const index = activeNames.value.indexOf(item)
    if (index === -1) {
      activeNames.value.push(item)
    } else {
      activeNames.value.splice(index, 1)
    }
  }
  emits('update:modelValue', activeNames.value)
  emits('change', activeNames.value)
}

provide(collapseContextKey, {
  activeNames,
  handleItemClick,
})
</script>

<style lang="scss" scoped></style>
