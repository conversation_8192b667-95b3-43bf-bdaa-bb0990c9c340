<template>
  <i
    class="vk-icon"
    :class="{
      [`vk-icon--${type}`]: type,
    }"
    :style="customStyles"
    v-bind="$attrs"
  >
    <font-awesome-icon v-bind="filteredProps"></font-awesome-icon>
  </i>
</template>

<script lang="ts" setup>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import type { FontAwesomeIconProps } from './types'
import { omit } from 'lodash-es'
import { computed } from 'vue'

defineOptions({
  name: 'VKIcon',
  inheritAttrs: false,
})
const props = defineProps<FontAwesomeIconProps>()
const filteredProps = computed(() => omit(props, ['type', 'color']))
const customStyles = computed(() => (props.color ? { color: props.color } : ''))
</script>

<style lang="scss" scoped></style>
